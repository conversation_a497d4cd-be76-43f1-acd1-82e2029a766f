// @ts-check

import { toRecordId } from "../infra/db/surrealdb/toRecordId.js";

/**
 * @param {Object} options
 * @param {Record<string, any>} options.entity
 * @param {Record<string, any>} options.payload
 * @param {boolean} [options.checkId=false]
 */
export function validateEntityFields({ entity, payload, checkId = false }) {
	for (const field of entity.fields) {
		if (!payload[field] && checkId && field !== "id") {
			throw new Error(`${entity.constructor.name} must have a ${field}`);
		}
	}
}

/**
 * @param {any} entity
 * @param {string} [id]
 * @param {string} [field="id"]
 */
export function buildEntityId(entity, id, field = "id") {
	if (id) {
		const recordId = toRecordId(id);

		if (recordId) {
			entity[field] = recordId;
		}
	}
}
