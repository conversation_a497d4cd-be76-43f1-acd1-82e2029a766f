// @ts-check

import { RecordId } from "surrealdb";
import { buildEntityId, validateEntityFields } from "../../shared/domain/entityService.js";

/**
 * @typedef {Object} ICharacterDto
 * @property {string} id
 * @property {string} user
 * @property {string} name
 * @property {number} health
 * @property {number} money
 * @property {number[]} position
 * @property {string} createdAt
 */

/**
 * @typedef {Object} INewCharacterDto
 * @property {string} [id]
 * @property {string} user
 * @property {string} name
 */

export class Character {
	static fields = /** @type {const} */ (["id", "user", "name"]);

	/** @type {RecordId | undefined} */
	id;
	/** @type {string} */
	user;
	/** @type {string} */
	name;
	/** @type {number} */
	health;
	/** @type {number} */
	money;
	/** @type {number[]} */
	position;
	/** @type {string | undefined} */
	createdAt;

	/**
	 * @private
	 */
	constructor(payload) {
		validateEntityFields({ entity: Character, payload });

		buildEntityId(this, payload.id);
		buildEntityId(this, payload.user, "user");
		this.name = payload.name;
		this.health = payload.health;
		this.money = payload.money;
		this.position = payload.position;
		this.createdAt = payload.createdAt;
	}

	/**
	 * @param {ICharacterDto} payload
	 * @returns {Character}
	 */
	static build(payload) {
		return new Character(payload);
	}

	/**
	 * @param {INewCharacterDto} payload
	 * @returns {Character}
	 */
	static buildNew(payload) {
		return new Character({
			user: payload.user,
			name: payload.name,
			health: 100,
			money: 0,
			position: [0, 0, 0],
		});
	}
}
