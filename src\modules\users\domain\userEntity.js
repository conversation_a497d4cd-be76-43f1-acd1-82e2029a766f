// @ts-check

/**
 * @typedef {Object} IUserDto
 * @property {string} id
 * @property {string} username
 * @property {string} email
 */

export class UserEntity {
	static fields = /** @type {const} */ (["id", "username", "email"]);

	/**
	 * @param {IUserDto} payload
	 */
	constructor(payload) {
		for (const field of UserEntity.fields) {
			if (!payload[field]) {
				throw new Error(`${this.constructor.name} must have a ${field}`);
			}
		}

		this.id = payload.id;
		this.username = payload.username;
		this.email = payload.email;
	}

	/**
	 * @param {IUserDto} payload
	 * @returns {UserEntity}
	 */
	static build(payload) {
		return new UserEntity(payload);
	}
}
