import { getSurreal } from "../../shared/infra/db/surrealdb/client.js";
import { dbResultHandler } from "../../shared/infra/db/surrealdb/dbResultHandler.js";
// biome-ignore lint/correctness/noUnusedImports: used for jsdoc
import { UserEntity } from "../domain/userEntity.js";

export const usersRepository = {
	/**
	 * @param {UserEntity} payload
	 */
	insertOne: async (payload) => {
		const response = await dbResultHandler.try(getSurreal().create("users", payload));

		return dbResultHandler.findOne(response);
	},

	findOne: async ({ email }) => {
		const response = await dbResultHandler.try(
			getSurreal().query(
				/* surrealql */ `
					SELECT email
					FROM ONLY users
					WHERE email = $email
					LIMIT 1
				`,
				{
					email,
				},
			),
		);

		return dbResultHandler.findOne(response);
	},
};
